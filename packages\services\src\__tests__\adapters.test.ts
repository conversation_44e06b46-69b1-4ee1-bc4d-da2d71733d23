import { describe, it, expect } from 'vitest'
import { buildODataParams } from '../adapters/odataAdapter'
import { buildRestParams } from '../adapters/restAdapter'

describe('OData Adapter', () => {
  it('should build OData parameters correctly', () => {
    const odata = {
      filter: "Name eq '{{productName}}'",
      select: 'Id,Name,Price',
      orderby: 'Name asc',
      top: 10,
      skip: 0,
      count: true
    }
    
    const context = {
      productName: 'Laptop'
    }
    
    const result = buildODataParams(odata, context)
    
    expect(result).toEqual({
      '$filter': "Name eq 'Laptop'",
      '$select': 'Id,Name,Price',
      '$orderby': 'Name asc',
      '$top': 10,
      '$skip': 0,
      '$count': 'true'
    })
  })

  it('should handle partial OData parameters', () => {
    const odata = {
      filter: "Status eq '{{status}}'",
      top: 5
    }
    
    const context = {
      status: 'active'
    }
    
    const result = buildODataParams(odata, context)
    
    expect(result).toEqual({
      '$filter': "Status eq 'active'",
      '$top': 5
    })
  })
})

describe('REST Adapter', () => {
  it('should build REST parameters correctly', () => {
    const params = {
      page: '{{currentPage}}',
      limit: 10,
      search: '{{searchTerm}}'
    }
    
    const context = {
      currentPage: 2,
      searchTerm: 'test'
    }
    
    const result = buildRestParams(params, context)
    
    expect(result).toEqual({
      page: 2,
      limit: 10,
      search: 'test'
    })
  })
})
