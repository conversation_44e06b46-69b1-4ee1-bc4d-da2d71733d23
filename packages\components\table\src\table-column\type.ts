import { ValueEnum } from '@neue-plus/components/pro-table'
import { buildProps } from '@neue-plus/utils'
import type { TableColumnCtx } from 'element-plus'
import type { ExtractPropTypes } from 'vue'

// 继承Element Plus的TableColumnCtx，只添加自定义属性
export const neTableColumnProps = buildProps({
  // 自定义属性：值枚举
  valueEnum: {
    type: Object as () => ValueEnum,
  },
} as const)

export type NeTableColumnProps = ExtractPropTypes<typeof neTableColumnProps> &
  Partial<TableColumnCtx<any>>
