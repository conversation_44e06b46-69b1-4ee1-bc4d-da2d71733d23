# @neue-plus/services

A flexible API service layer supporting REST and OData protocols with template parsing capabilities.

## Features

- 🚀 **Multiple Protocols**: Support for both REST and OData APIs
- 🔧 **Template Parsing**: Dynamic parameter substitution using `{{key}}` syntax
- 📦 **Lightweight**: Minimal dependencies with optional UI integration
- 🎯 **TypeScript**: Full TypeScript support with type definitions
- 🔌 **Flexible Error Handling**: Customizable error handling with optional Element Plus integration

## Installation

```bash
npm install @neue-plus/services
# or
yarn add @neue-plus/services
# or
pnpm add @neue-plus/services
```

## Basic Usage

### Simple REST API Call

```typescript
import { executeApi } from '@neue-plus/services'

const apiSchema = {
  url: 'https://api.example.com/users',
  method: 'get' as const,
  protocol: 'rest' as const
}

const result = await executeApi(apiSchema, {})
console.log(result.data)
```

### With Template Parameters

```typescript
const apiSchema = {
  url: 'https://api.example.com/users/{{userId}}',
  method: 'get' as const,
  headers: {
    'Authorization': 'Bearer {{token}}'
  },
  params: {
    'page': '{{currentPage}}',
    'limit': 10
  }
}

const context = {
  userId: 123,
  token: 'your-auth-token',
  currentPage: 1
}

const result = await executeApi(apiSchema, context)
```

### OData Support

```typescript
const odataSchema = {
  url: 'https://api.example.com/odata/Products',
  method: 'get' as const,
  protocol: 'odata' as const,
  odata: {
    filter: "Name eq '{{productName}}'",
    select: 'Id,Name,Price',
    top: 10,
    orderby: 'Name asc'
  }
}

const context = {
  productName: 'Laptop'
}

const result = await executeApi(odataSchema, context)
```

## API Reference

### Types

#### `ApiSchema`

```typescript
interface ApiSchema {
  url: string
  method: 'get' | 'post' | 'put' | 'delete'
  protocol?: 'rest' | 'odata'
  headers?: Record<string, any>
  params?: Record<string, any>
  body?: any
  dataPath?: string
  map?: { label: string; value: string }
  odata?: {
    filter?: string
    orderby?: string
    select?: string
    expand?: string
    top?: number
    skip?: number
    count?: boolean
  }
}
```

#### `ApiResponse`

```typescript
interface ApiResponse<T = any> {
  data: T
  error?: string
  status?: number
  [key: string]: any
}
```

### Functions

#### `executeApi(schema, context)`

Executes an API call based on the provided schema and context.

- **Parameters:**
  - `schema: ApiSchema` - The API configuration
  - `context: Record<string, any>` - Variables for template substitution

- **Returns:** `Promise<ApiResponse>`

#### `parseExpression(obj, context)`

Parses template expressions in objects, arrays, or strings.

- **Parameters:**
  - `obj: any` - The object to parse
  - `context: Record<string, any>` - Variables for substitution

- **Returns:** `any` - Parsed object with substituted values

#### `setErrorHandler(handler)`

Sets a custom error handler for API errors.

- **Parameters:**
  - `handler: (message: string) => void` - Custom error handler function

```typescript
import { setErrorHandler } from '@neue-plus/services'

setErrorHandler((message) => {
  // Custom error handling logic
  console.error('API Error:', message)
  // Show toast, log to service, etc.
})
```

## Advanced Usage

### Custom Data Path

```typescript
const schema = {
  url: 'https://api.example.com/data',
  method: 'get' as const,
  dataPath: 'result.items' // Extract data from nested path
}
```

### POST Request with Body

```typescript
const schema = {
  url: 'https://api.example.com/users',
  method: 'post' as const,
  headers: {
    'Content-Type': 'application/json'
  },
  body: {
    name: '{{userName}}',
    email: '{{userEmail}}'
  }
}

const context = {
  userName: 'John Doe',
  userEmail: '<EMAIL>'
}
```

## Element Plus Integration

If you're using Element Plus in your project, error messages will automatically use `ElMessage.error()`. If Element Plus is not available, errors will fall back to console logging or your custom error handler.

## Development

```bash
# Install dependencies
pnpm install

# Build the package
pnpm build

# Run tests
pnpm test

# Run tests with coverage
pnpm test:coverage

# Type checking
pnpm typecheck
```

## License

MIT

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.
