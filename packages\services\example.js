// 简单的使用示例
const { parseExpression, setErrorHandler } = require('./dist/index.js')

// 设置自定义错误处理器
setErrorHandler((message) => {
  console.log('🚨 Custom Error Handler:', message)
})

// 测试模板解析功能
console.log('=== 测试模板解析功能 ===')

const context = {
  user: {
    id: 123,
    name: '<PERSON>',
    email: '<EMAIL>',
  },
  token: 'abc123',
  page: 1,
}

// 测试字符串模板
console.log('字符串模板:', parseExpression('{{user.name}}', context))
console.log('混合字符串:', parseExpression("Name eq '{{user.name}}'", context))
console.log(
  '多个变量:',
  parseExpression('Hello {{user.name}}, your ID is {{user.id}}', context)
)

// 测试对象模板
const objTemplate = {
  userId: '{{user.id}}',
  userName: '{{user.name}}',
  authToken: '{{token}}',
}
console.log('对象模板:', parseExpression(objTemplate, context))

// 测试数组模板
const arrTemplate = ['{{user.name}}', '{{user.email}}', '{{token}}']
console.log('数组模板:', parseExpression(arrTemplate, context))

console.log('\n✅ 模板解析测试完成！')

// 注意：executeApi 需要真实的网络请求，这里只是展示用法
console.log('\n=== API 调用示例（仅展示配置） ===')

const restApiSchema = {
  url: 'https://jsonplaceholder.typicode.com/users/{{userId}}',
  method: 'get',
  protocol: 'rest',
  headers: {
    Authorization: 'Bearer {{token}}',
  },
}

const odataApiSchema = {
  url: 'https://api.example.com/odata/Products',
  method: 'get',
  protocol: 'odata',
  odata: {
    filter: "Name eq '{{productName}}'",
    select: 'Id,Name,Price',
    top: 10,
  },
}

console.log('REST API 配置:', JSON.stringify(restApiSchema, null, 2))
console.log('OData API 配置:', JSON.stringify(odataApiSchema, null, 2))

console.log('\n🎉 @neue-plus/services 包测试完成！')
