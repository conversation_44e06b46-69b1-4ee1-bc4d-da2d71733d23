export type Protocol = 'rest' | 'odata'

export interface ApiSchema {
  url: string
  method: 'get' | 'post' | 'put' | 'delete'
  protocol?: Protocol
  headers?: Record<string, any>
  params?: Record<string, any>
  body?: any
  dataPath?: string
  map?: { label: string; value: string }

  // odata专用
  odata?: {
    filter?: string
    orderby?: string
    select?: string
    expand?: string
    top?: number
    skip?: number
    count?: boolean
  }
}

export interface ApiResponse<T = any> {
  data: T
  error?: string
  status?: number
  [key: string]: any
}
