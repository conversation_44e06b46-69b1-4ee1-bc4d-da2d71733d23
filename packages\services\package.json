{"name": "@neue-plus/services", "version": "1.0.0", "description": "A flexible API service layer supporting REST and OData protocols with template parsing", "keywords": ["api", "rest", "odata", "http", "request", "service", "template", "parser"], "homepage": "https://neue-plus.org", "bugs": {"url": "https://github.com/neue-plus/neue-plus/issues"}, "license": "MIT", "author": "Neue Plus Team", "files": ["dist", "README.md"], "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "repository": {"type": "git", "url": "https://github.com/neue-plus/neue-plus.git", "directory": "packages/services"}, "scripts": {"build": "tsup src/index.ts --dts --format esm,cjs --clean", "dev": "tsup src/index.ts --dts --format esm,cjs --watch", "prepublishOnly": "npm run build", "test": "vitest", "test:coverage": "vitest --coverage", "typecheck": "tsc --noEmit"}, "peerDependencies": {"element-plus": "^2.0.0"}, "peerDependenciesMeta": {"element-plus": {"optional": true}}, "dependencies": {"axios": "^1.6.0", "lodash-unified": "^1.0.2"}, "devDependencies": {"@types/node": "^22.9.0", "tsup": "^8.4.0", "typescript": "^5.0.0", "vitest": "^2.0.5"}, "engines": {"node": ">=16"}}