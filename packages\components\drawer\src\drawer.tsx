import { defineComponent, ref } from 'vue'
import { El<PERSON>rawer } from 'element-plus'
import { useEventContext } from '../../material-render/src/context/event-flow'
import { neDrawerProps } from './types'

const NeDrawer = defineComponent({
  name: 'NeDrawer',
  props: neDrawerProps,
  setup(props, { attrs, slots, expose }) {
    const { refs } = useEventContext()
    expose({
      open: () => {
        console.log('open drawer')
        visible.value = true
      },
      close: () => {
        visible.value = false
      },
    })
    const visible = ref(props.modelValue || false)
    const closed = () => {
      Object.entries(attrs.eventsMap || {}).forEach(([target, actionType]) => {
        if (actionType === 'refresh') {
          refs[target]?.refresh?.()
        }
      })
    }
    const close = () => {
      console.log('close')
    }
    return () => (
      <ElDrawer
        {...props}
        v-model={visible.value}
        onClosed={closed}
        onClose={close}
      >
        {{
          ...slots,
        }}
      </ElDrawer>
    )
  },
})

export default NeDrawer
