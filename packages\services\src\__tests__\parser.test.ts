import { describe, it, expect } from 'vitest'
import { parseExpression } from '../parser'

describe('parseExpression', () => {
  const context = {
    user: {
      id: 123,
      name: '<PERSON>',
      email: '<EMAIL>'
    },
    token: 'abc123',
    page: 1
  }

  it('should parse string templates', () => {
    expect(parseExpression('{{user.name}}', context)).toBe('<PERSON>')
    expect(parseExpression('{{user.id}}', context)).toBe(123)
    expect(parseExpression('{{token}}', context)).toBe('abc123')
  })

  it('should parse object templates', () => {
    const obj = {
      userId: '{{user.id}}',
      userName: '{{user.name}}',
      authToken: '{{token}}'
    }
    
    const result = parseExpression(obj, context)
    
    expect(result).toEqual({
      userId: 123,
      userName: '<PERSON>',
      authToken: 'abc123'
    })
  })

  it('should parse array templates', () => {
    const arr = ['{{user.name}}', '{{user.email}}', '{{token}}']
    
    const result = parseExpression(arr, context)
    
    expect(result).toEqual(['John Doe', '<EMAIL>', 'abc123'])
  })

  it('should handle nested objects and arrays', () => {
    const complex = {
      user: {
        info: ['{{user.name}}', '{{user.email}}'],
        meta: {
          id: '{{user.id}}',
          token: '{{token}}'
        }
      },
      pagination: {
        page: '{{page}}'
      }
    }
    
    const result = parseExpression(complex, context)
    
    expect(result).toEqual({
      user: {
        info: ['John Doe', '<EMAIL>'],
        meta: {
          id: 123,
          token: 'abc123'
        }
      },
      pagination: {
        page: 1
      }
    })
  })

  it('should return primitive values unchanged', () => {
    expect(parseExpression(123, context)).toBe(123)
    expect(parseExpression(true, context)).toBe(true)
    expect(parseExpression(null, context)).toBe(null)
    expect(parseExpression(undefined, context)).toBe(undefined)
  })

  it('should handle missing context keys', () => {
    expect(parseExpression('{{missing.key}}', context)).toBe(undefined)
  })
})
