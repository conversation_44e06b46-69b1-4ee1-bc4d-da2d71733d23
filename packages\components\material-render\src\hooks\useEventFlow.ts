import { ActionNode } from '../../types'
import { useEventContext } from '../context/event-flow'

/**
 * 类型定义
 */

export interface EventNode {
  action: ActionNode
  next: EventNode | null
}
/**
 * useEventFlow - 基于链表的串行执行器，配合事件类型处理器
 */
export function useEventFlow() {
  const { refs, handlers } = useEventContext()
  /**
   * 运行行为数组
   */
  function run(actions: ActionNode[] = [], params: Record<string, any> = {}) {
    const linkedList = toLinkedList(actions)
    if (linkedList) exec(linkedList, params)
  }

  /**
   * 将数组转换为链表结构
   */
  function toLinkedList(actions: ActionNode[]) {
    let head: EventNode | null = null
    let current: EventNode | null = null

    for (const action of actions) {
      const node: EventNode = { action, next: null }
      if (!head) head = node
      else if (current) current.next = node
      current = node
    }

    return head
  }

  /**
   * 串行执行链表节点
   */
  async function exec(node: EventNode | null, params: Record<string, any>) {
    if (!node || !node.action) return

    const { actionType = '', delay = 0, target = '' } = node.action.config || {}
    if (!actionType) {
      await exec(node.next, params)
      return
    }
    // 可选延迟
    if (delay > 0) await new Promise((resolve) => setTimeout(resolve, delay))

    const handlerName = `${actionType}`
    const handler = refs[target][actionType] || handlers[actionType]
    if (typeof handler === 'function') {
      try {
        await handler(node.action, params, refs)
      } catch (error) {
        console.error(`[EventFlow] 执行 ${actionType} 出错：`, error)
      }
    } else {
      console.warn(`[EventFlow] 未定义 handler: ${handlerName}`)
    }

    // 继续执行下一个
    await exec(node.next, params)
  }

  return { run }
}
