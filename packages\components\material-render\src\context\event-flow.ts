// src/context/event-flow.ts
import { inject, provide, reactive } from 'vue'

export const REFS_KEY = Symbol('REFS')
export const HANDLERS_KEY = Symbol('HANDLERS')

export function provideEventContext(handlers: Record<string, any>) {
  const refs = reactive<Record<string, any>>({})
  provide(REFS_KEY, refs)
  provide(HANDLERS_KEY, handlers)
  return refs
}

export function useEventContext() {
  const refs = inject<Record<string, any>>(REFS_KEY)
  const handlers = inject<Record<string, any>>(HANDLERS_KEY)
  if (!refs || !handlers) throw new Error('Event context is not provided')
  return { refs, handlers }
}
