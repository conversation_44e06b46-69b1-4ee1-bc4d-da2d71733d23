import { computed, defineComponent, inject, resolveDynamicComponent } from 'vue'
import { getComponentByName } from '../utils'
import { ActionNode, ElementEvent } from '../../types'
import { REFS_KEY } from '../context/event-flow'
import { useEventFlow } from '../hooks/useEventFlow'
import { neRenderCoreProps } from './types'

export default defineComponent({
  name: 'NeRenderCore',
  props: neRenderCoreProps,
  setup(props) {
    const eventFlow = useEventFlow()
    const refs = inject(REFS_KEY, {}) as Record<string, any>
    const elProps = computed(() => props.props || {})
    const elements = computed(() => props.elements || [])
    const resolvedComponent = computed(() => {
      return getComponentByName(props.type)
    })
    const dynamicEvents = computed(() => {
      const result: Record<string, (...args: any[]) => void> = {}
      props.events.forEach((evt: ElementEvent) => {
        if (evt.eventName && Array.isArray(evt.actions)) {
          result[evt.eventName] = (arg0, arg1, arg2) => {
            eventFlow?.run(evt.actions, {
              id: props.id,
              clickParams: [arg0, arg1, arg2],
            })
          }
        }
      })
      return result
    })
    const eventsMap = computed(() => {
      let result: Record<string, string> = {}
      props.events.forEach((evt: ElementEvent) => {
        const map = evt.actions.reduce(
          (acc: Record<string, string>, cur: ActionNode) => {
            const { actionType = '', target = '' } = cur.config || {}
            if (actionType && target) {
              acc[target] = actionType
            }
            return acc
          },
          {}
        )
        result = { ...result, ...map }
      })
      return Object.keys(result).length ? result : undefined
    })
    const setRef = (el: any) => {
      if (props.id && el) {
        refs[props.id] = el
      }
    }

    return () => {
      const DynamicTag = resolveDynamicComponent(resolvedComponent.value) as any
      const NeRenderCore = resolveDynamicComponent('NeRenderCore') as any
      const defaultSlot = elements.value?.length
        ? () =>
            elements.value.map((child, index) => (
              <NeRenderCore {...child} key={index} />
            ))
        : undefined
      const dynamicSlots = props.slots
        ? Object.fromEntries(
            Object.entries(props.slots).map(([slotName, slotValue]) => [
              slotName,
              () =>
                Array.isArray(slotValue)
                  ? slotValue.map((child: any, index: number) => (
                      <NeRenderCore {...child} key={index} />
                    ))
                  : slotValue,
            ])
          )
        : {}
      return (
        <DynamicTag
          ref={setRef}
          {...elProps.value}
          {...dynamicEvents.value}
          eventsMap={eventsMap.value}
        >
          {{
            default: defaultSlot,
            ...dynamicSlots,
          }}
        </DynamicTag>
      )
    }
  },
})
