import { buildProps } from '@neue-plus/utils'
import { ElementEvent, SchemaElement } from '../../types'
import type { ExtractPropTypes } from 'vue'

export const neRenderCoreProps = buildProps({
  id: {
    type: String,
    required: true,
  },
  type: {
    type: String,
    required: true,
  },
  name: {
    type: String,
  },
  props: {
    type: Object as () => {
      [key: string]: any
    },
  },
  slots: {
    type: Object as () => {
      [key: string]: any
    },
  },
  elements: {
    type: Array as () => SchemaElement[],
    default: () => [],
  },
  events: {
    type: Array as () => ElementEvent[],
    default: () => [],
  },
} as const)

export type NeMaterialRenderProps = ExtractPropTypes<typeof neRenderCoreProps>

// 事件类型
export type NeMaterialRenderEmits = {
  (e: 'beforeRender', node: any): void
  (e: 'afterRender', vnode: any, node: any): void
}
