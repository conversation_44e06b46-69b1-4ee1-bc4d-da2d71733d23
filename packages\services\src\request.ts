import axios from 'axios'

// 可选的错误处理函数类型
export type ErrorHandler = (message: string) => void

// 默认的错误处理函数
const defaultErrorHandler: ErrorHandler = (message: string) => {
  console.error('[API Error]:', message)
}

// 全局错误处理器，可以被外部设置
let globalErrorHandler: ErrorHandler = defaultErrorHandler

/**
 * 设置全局错误处理器
 * @param handler 错误处理函数
 */
export function setErrorHandler(handler: ErrorHandler) {
  globalErrorHandler = handler
}

/**
 * 尝试使用 Element Plus 的消息组件，如果不可用则使用默认处理器
 * @param message 错误消息
 */
function handleError(message: string) {
  try {
    // 尝试动态导入 element-plus
    const { ElMessage } = require('element-plus')
    if (ElMessage && typeof ElMessage.error === 'function') {
      ElMessage.error(message)
      return
    }
  } catch {
    // element-plus 不可用，使用全局错误处理器
  }

  globalErrorHandler(message)
}

const request = axios.create({
  timeout: 8000,
})

request.interceptors.request.use((config) => {
  // 可添加 token 等
  return config
})

request.interceptors.response.use(
  (res) => res.data,
  (err) => {
    const message = err.message || '请求失败'
    handleError(message)
    return Promise.reject(err)
  }
)

export default request
