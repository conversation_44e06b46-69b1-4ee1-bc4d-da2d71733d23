<template>
  <el-table v-bind="{ ...props, ...$attrs }" ref="table">
    <el-table-column
      v-for="column in columns"
      :key="column.prop || column.label"
      v-bind="column"
    />
    <slot />
  </el-table>
</template>

<script lang="ts" setup>
import { computed, defineExpose, ref } from 'vue'
import { neTableProps } from './table'

defineOptions({
  name: 'NeTable',
  inheritAttrs: false,
})
const table = ref<any>()

const props = defineProps(neTableProps)
const columns = computed(() => {
  return props.columns.map((column) => {
    return {
      ...column,
    }
  })
})

const expand = ref(true)
const toggleExpandAll = (collapse: boolean = expand.value) => {
  expand.value = !collapse
  /**
   * Recursively expands/collapses table rows for nodes and their children.
   * @param {Array} nodes - Array of node objects to expand/collapse
   * @param {boolean} collapse - Expansion flag (true to expand, false to collapse)
   */
  const expandRecursive = (nodes: any[]) => {
    const children = props.treeProps.children || 'children'
    nodes?.forEach((node) => {
      table.value.toggleRowExpansion(node, collapse)
      if (node[children] && node[children].length) {
        expandRecursive(node[children])
      }
    })
  }
  expandRecursive(props.data)
}
defineExpose({
  toggleExpandAll,
})
</script>
