<!-- eslint-disable @typescript-eslint/no-unused-vars -->

<template>
  <div class="ne-pro-table">
    <!-- 搜索表单 - 使用Dynamic Form -->
    <div v-if="searchConfig || filterColumns.length > 0">
      <ne-dynamic-form
        v-model="filters"
        v-bind="finalSearchConfig"
        :form-items="filterColumns"
        @submit="handleSearch"
        @reset="handleReset"
        @change="handleFilterChange"
      >
        <template #extra-buttons>
          <el-button @click="handleRefresh">刷新</el-button>
        </template>
      </ne-dynamic-form>
    </div>

    <!-- 表格 -->
    <div class="ne-pro-table__table">
      <NeTable
        v-bind="tableProps"
        :data="dataSource"
        :columns="visibleColumns"
        :loading="loading"
        @sort-change="handleSortChange"
      >
        <slot />
      </NeTable>
    </div>

    <!-- 分页 -->
    <div v-if="showPagination" class="ne-pro-table__pagination">
      <el-pagination
        v-bind="pagination"
        :current-page="pagination.current"
        :page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref, watch } from 'vue'
import NeDynamicForm from '../../dynamic-form'
import NeTable from '../../table'
import { type ProTableFilters, proTableProps } from './pro-table'

defineOptions({
  name: 'NeProTable',
  inheritAttrs: false,
})

const props = defineProps(proTableProps)

const emit = defineEmits([
  'filter-change',
  'page-change',
  'sort-change',
  'refresh',
])
const tableProps = computed(() => {
  const { data, columns, loading, bordered, size } = props
  return { data, columns, loading, bordered, size }
})

// 筛选数据
const filters = ref<ProTableFilters>({})

// 可筛选的列
const filterColumns = computed(() => {
  return props.columns
    .filter((column) => column.hideInForm)
    .map((column) => ({
      ...column,
      valueType: column.valueType ?? 'text',
    }))
})

// 可见的列
const visibleColumns = computed(() => {
  return props.columns
    ?.filter((column) => !column.hideInTable)
    .map((column) => ({
      ...column,
      valueType: column.valueType ?? 'text',
    }))
})

const loading = ref(false)
const pagination = ref({ current: 1, pageSize: 10, total: 0 })
const dataSource = ref<any[]>(props.data)
// 最终的搜索表单配置
const finalSearchConfig = computed(() => {
  // 如果提供了自定义搜索配置，直接使用
  if (props.searchConfig) {
    return {
      ...props.searchConfig,
    }
  }

  return {
    submitText: '搜索',
    resetText: '重置',
  }
})

// 初始化筛选表单
const initFilters = () => {
  const newFilters: ProTableFilters = {}
  filterColumns.value.forEach((column) => {
    if (column.prop) {
      newFilters[column.prop] = column.valueType === 'dateRange' ? [] : ''
    }
  })
  filters.value = newFilters
}

// 筛选变化处理
const handleFilterChange = () => {
  fetchData()
  emit('filter-change', filters.value)
}

// 搜索
const handleSearch = () => {
  fetchData()
  emit('filter-change', filters.value)
}

// 重置筛选
const handleReset = () => {
  initFilters()
  fetchData()
  emit('filter-change', filters.value)
}

// 刷新
const handleRefresh = () => {
  fetchData()
  emit('refresh')
}

// 分页大小变化
const handleSizeChange = (pageSize: number) => {
  pagination.value.pageSize = pageSize
  pagination.value.current = 1
  fetchData()
  emit('page-change', 1, pageSize)
}

// 当前页变化
const handleCurrentChange = (current: number) => {
  pagination.value.current = current
  fetchData()
  emit('page-change', current, pagination.value.pageSize)
}

// 排序变化
const handleSortChange = ({
  prop,
  order,
}: {
  prop: string
  order: 'ascending' | 'descending' | null
}) => {
  // 可将排序参数存储到本地，然后 fetchData({ sortProp: prop, sortOrder: order })
  fetchData({ sortProp: prop, sortOrder: order })
  emit('sort-change', prop, order)
}

// 监听columns变化，重新初始化筛选表单
watch(
  () => props.columns,
  () => {
    initFilters()
  },
  { immediate: true }
)
watch(
  () => props.data,
  (val) => {
    dataSource.value = val
  }
)
async function fetchData(extraParams = {}) {
  if (typeof props.request === 'function') {
    loading.value = true
    try {
      const params = {
        ...pagination.value,
        ...filters.value,
        ...extraParams,
        // 可扩展排序参数
      }
      const { data, total } = await props.request(params)
      dataSource.value = data
      pagination.value.total = total || data.length
    } finally {
      loading.value = false
    }
  } else {
    dataSource.value = props.data
  }
}

onMounted(() => {
  console.log('props:123', props)
  fetchData()
})
</script>
