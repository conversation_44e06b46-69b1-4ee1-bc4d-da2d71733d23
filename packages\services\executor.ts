import { get } from 'lodash-unified'
import request from './request'
import { parseExpression } from './parser'
import { buildODataParams } from './adapters/odataAdapter'
import type { ApiSchema } from './types'

export async function executeApi(api: ApiSchema, context: Record<string, any>) {
  const protocol = api.protocol || 'rest'
  const method = (api.method || 'get').toLowerCase()

  if (['get', 'post', 'put', 'delete'].includes(method) === false) {
    throw new Error(`Invalid method: ${method}`)
  }
  const parsedHeaders = parseExpression(api.headers || {}, context)

  const query =
    protocol === 'odata'
      ? buildODataParams(api.odata || {}, context)
      : parseExpression(api.params || {}, context)

  const data = parseExpression(api.body || {}, context)
  const res = await request({
    url: api.url,
    method,
    headers: parsedHeaders,
    params: query,
    data: method !== 'get' ? data : undefined,
  })

  const raw = get(
    res,
    api.dataPath || (protocol === 'odata' ? 'value' : 'data')
  )
  return {
    data: raw,
    page: 1,
    success: true,
    total: 0,
  }
}
