import { get } from 'lodash-unified'

/**
 * 解析包含 {{key}} 模板语法的任意对象结构
 * 支持 string / object / array / 原始类型
 */
export function parseExpression(obj: any, context: Record<string, any>): any {
  // 1. 字符串：替换模板
  if (typeof obj === 'string') {
    // 如果字符串完全是一个模板变量（如 "{{user.name}}"），直接返回变量值
    const fullMatch = obj.match(/^{{\s*(.*?)\s*}}$/)
    if (fullMatch) {
      return get(context, fullMatch[1] ?? '')
    }

    // 如果字符串包含模板变量（如 "Name eq '{{productName}}'"），进行字符串替换
    return obj.replace(/{{\s*(.*?)\s*}}/g, (match, key) => {
      const value = get(context, key.trim())
      return value !== undefined ? String(value) : match
    })
  }

  // 2. 数组：递归每一项
  if (Array.isArray(obj)) {
    return obj.map((item) => parseExpression(item, context))
  }

  // 3. 对象：递归每个 key
  if (obj && typeof obj === 'object') {
    const result: Record<string, any> = {}
    for (const key in obj) {
      result[key] = parseExpression(obj[key], context)
    }
    return result
  }

  // 4. 原始值（number、boolean、null）直接返回
  return obj
}
