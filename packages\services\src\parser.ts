import { get } from 'lodash-unified'

/**
 * 解析包含 {{key}} 模板语法的任意对象结构
 * 支持 string / object / array / 原始类型
 */
const extractFirstBracesContent = (str: string): string | null => {
  const match = str.match(/{{\s*(.*?)\s*}}/)
  return match?.[1] ?? null
}

export function parseExpression(obj: any, context: Record<string, any>): any {
  // 1. 字符串：替换模板
  if (typeof obj === 'string') {
    const match = extractFirstBracesContent(obj)
    return get(context, match ?? '')
  }

  // 2. 数组：递归每一项
  if (Array.isArray(obj)) {
    return obj.map((item) => parseExpression(item, context))
  }

  // 3. 对象：递归每个 key
  if (obj && typeof obj === 'object') {
    const result: Record<string, any> = {}
    for (const key in obj) {
      result[key] = parseExpression(obj[key], context)
    }
    return result
  }

  // 4. 原始值（number、boolean、null）直接返回
  return obj
}
